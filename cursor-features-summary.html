<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor IDE 近半年重要特性总结</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            padding: 60px 0;
            color: white;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            animation: fadeInUp 1s ease-out;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .timeline {
            position: relative;
            margin: 40px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            animation: slideInFromLeft 0.8s ease-out;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:nth-child(even) {
            animation: slideInFromRight 0.8s ease-out;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 30px;
            width: 30px;
            height: 30px;
            background: #667eea;
            border-radius: 50%;
            border: 4px solid white;
        }

        .feature-card:nth-child(even)::before {
            right: -15px;
            left: auto;
        }

        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 24px;
            color: white;
        }

        .feature-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 5px;
        }

        .feature-date {
            color: #666;
            font-size: 0.9rem;
        }

        .feature-description {
            color: #555;
            margin-bottom: 20px;
            line-height: 1.7;
        }

        .feature-highlights {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .feature-highlights h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .feature-highlights ul {
            list-style: none;
        }

        .feature-highlights li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }

        .feature-highlights li::before {
            content: '✨';
            position: absolute;
            left: 0;
            top: 8px;
        }

        .demo-placeholder {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            border: 2px dashed #ccc;
            position: relative;
            overflow: hidden;
        }

        .demo-placeholder::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 2s infinite;
        }

        .demo-text {
            color: #666;
            font-style: italic;
            position: relative;
            z-index: 1;
        }

        .video-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .video-caption {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .feature-demo {
            margin: 20px 0;
        }

        .memories-demo {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 30px;
            border-radius: 15px;
            border: none;
        }

        .memory-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .memory-card h4 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
        }

        .memory-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: transform 0.2s ease;
        }

        .memory-item:hover {
            transform: translateX(5px);
        }

        .memory-tag {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 15px;
            min-width: 80px;
            text-align: center;
        }

        .memory-content {
            color: #555;
            flex: 1;
        }

        .mcp-demo {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 30px;
            border-radius: 15px;
            border: none;
        }

        .mcp-server-list {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .mcp-server-list h4 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
        }

        .mcp-server {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: transform 0.2s ease;
        }

        .mcp-server:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .server-name {
            color: #555;
            font-weight: 500;
        }

        .install-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
        }

        .install-btn:hover {
            transform: scale(1.05);
        }

        .jupyter-demo {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 30px;
            border-radius: 15px;
            border: none;
        }

        .notebook-interface {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .notebook-interface h4 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .notebook-cell {
            display: flex;
            margin: 15px 0;
            border-left: 3px solid #667eea;
            padding-left: 15px;
        }

        .cell-label {
            color: #667eea;
            font-weight: bold;
            margin-right: 15px;
            min-width: 60px;
        }

        .cell-content {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            flex: 1;
        }

        .cell-content code {
            color: #555;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .ai-suggestion {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 8px;
            animation: pulse 2s infinite;
        }

        .ai-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .chat-demo {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 30px;
            border-radius: 15px;
            border: none;
        }

        .chat-interface {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chat-interface h4 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
        }

        .chat-message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
        }

        .chat-message.user {
            background: #e3f2fd;
            margin-left: 20px;
        }

        .chat-message.ai {
            background: #f3e5f5;
            margin-right: 20px;
        }

        .message-author {
            font-weight: bold;
            color: #667eea;
            margin-right: 10px;
        }

        .message-content {
            color: #555;
        }

        .mermaid-diagram {
            background: #f8f9fa;
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }

        .diagram-box {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 10px auto;
            max-width: 150px;
            font-weight: bold;
        }

        .arrow {
            color: #667eea;
            font-size: 1.5rem;
            margin: 5px 0;
        }

        .tab-demo {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 30px;
            border-radius: 15px;
            border: none;
        }

        .code-editor {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .code-editor h4 {
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .file-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #333;
        }

        .file-tab {
            padding: 8px 16px;
            background: #2d2d2d;
            color: #ccc;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .file-tab.active {
            background: #1e1e1e;
            color: #fff;
            border-bottom: 2px solid #667eea;
        }

        .code-content {
            background: #1e1e1e;
            padding: 15px;
            border-radius: 5px;
        }

        .code-line {
            display: flex;
            margin: 5px 0;
            align-items: center;
        }

        .line-number {
            color: #666;
            margin-right: 15px;
            min-width: 20px;
            text-align: right;
            font-size: 0.9rem;
        }

        .code {
            color: #f8f8f2;
            font-size: 0.9rem;
        }

        .tab-suggestion {
            color: #667eea;
            background: rgba(102, 126, 234, 0.2);
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 5px;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.05);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInFromLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            100% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            color: white;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .timeline::before {
                left: 20px;
            }
            
            .feature-card::before {
                left: 5px;
            }
            
            .feature-card:nth-child(even)::before {
                left: 5px;
                right: auto;
            }
            
            .feature-card {
                margin-left: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Cursor IDE 近半年重要特性</h1>
            <p>AI 驱动的代码编辑器的革命性进展</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">1.0</div>
                <div class="stat-label">最新版本</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10+</div>
                <div class="stat-label">重大特性更新</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">AI 增强体验</div>
            </div>
        </div>

        <div class="timeline">
            <!-- BugBot 特性 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">🐛</div>
                    <div>
                        <div class="feature-title">BugBot - 自动代码审查</div>
                        <div class="feature-date">2025年6月 - v1.0</div>
                    </div>
                </div>
                <div class="feature-description">
                    BugBot 是 Cursor 1.0 的重磅特性，能够自动审查你的 Pull Request 并捕获潜在的 bug 和问题。当发现问题时，BugBot 会在 GitHub PR 中留下评论，你可以点击 "Fix in Cursor" 直接在编辑器中修复问题。
                </div>
                <div class="video-container">
                    <iframe width="100%" height="400" src="https://www.youtube.com/embed/cyPZd9_hQkE"
                            title="Amazon Q & Cursor Bugbot: Review & Fix Pull Requests"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                    </iframe>
                    <p class="video-caption">🎬 BugBot 自动代码审查实际演示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>自动检测代码中的潜在问题</li>
                        <li>直接在 GitHub PR 中提供反馈</li>
                        <li>一键修复功能，无缝集成到开发流程</li>
                        <li>支持多种编程语言和框架</li>
                    </ul>
                </div>
            </div>

            <!-- Background Agent 特性 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">☁️</div>
                    <div>
                        <div class="feature-title">Background Agent - 后台智能代理</div>
                        <div class="feature-date">2025年5月 - v0.50</div>
                    </div>
                </div>
                <div class="feature-description">
                    Background Agent 允许你并行运行多个 AI 代理来处理更大的任务。这些代理在远程环境中运行，你可以随时查看状态、发送后续指令或接管控制。这是编程效率的重大突破。
                </div>
                <div class="feature-demo">
                    <img src="https://www.cursor.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbg-agent.349d715a.png&w=3840&q=75"
                         alt="Background Agent 界面"
                         style="width: 100%; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <p class="video-caption">🎬 Background Agent 云端代理界面展示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>并行执行多个编程任务</li>
                        <li>远程环境中安全运行</li>
                        <li>实时状态监控和控制</li>
                        <li>适合大型项目和复杂重构</li>
                    </ul>
                </div>
            </div>

            <!-- Memories 特性 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">🧠</div>
                    <div>
                        <div class="feature-title">Memories - AI 记忆系统</div>
                        <div class="feature-date">2025年6月 - v1.0</div>
                    </div>
                </div>
                <div class="feature-description">
                    Memories 功能让 Cursor 能够记住对话中的重要信息，并在未来的对话中引用这些信息。记忆按项目存储，可以从设置中管理，大大提升了 AI 助手的上下文理解能力。
                </div>
                <div class="feature-demo">
                    <div class="demo-placeholder memories-demo">
                        <div class="memory-card">
                            <h4>💭 项目记忆示例</h4>
                            <div class="memory-item">
                                <span class="memory-tag">API设计</span>
                                <span class="memory-content">使用 RESTful 架构，JWT 认证</span>
                            </div>
                            <div class="memory-item">
                                <span class="memory-tag">数据库</span>
                                <span class="memory-content">PostgreSQL + Redis 缓存</span>
                            </div>
                            <div class="memory-item">
                                <span class="memory-tag">部署</span>
                                <span class="memory-content">Docker + Kubernetes</span>
                            </div>
                        </div>
                    </div>
                    <p class="video-caption">🎬 Memories 记忆系统概念演示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>跨对话保持上下文记忆</li>
                        <li>按项目独立存储记忆</li>
                        <li>可视化记忆管理界面</li>
                        <li>提升长期项目开发效率</li>
                    </ul>
                </div>
            </div>

            <!-- MCP 一键安装 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">🔧</div>
                    <div>
                        <div class="feature-title">MCP 一键安装与 OAuth 支持</div>
                        <div class="feature-date">2025年6月 - v1.0</div>
                    </div>
                </div>
                <div class="feature-description">
                    Model Context Protocol (MCP) 现在支持一键安装和 OAuth 认证。你可以轻松设置 MCP 服务器，并通过 OAuth 进行身份验证。Cursor 还提供了官方 MCP 服务器的精选列表。
                </div>
                <div class="feature-demo">
                    <div class="mcp-demo">
                        <div class="mcp-server-list">
                            <h4>🔧 MCP 服务器一键安装</h4>
                            <div class="mcp-server">
                                <span class="server-name">📊 Semantic Scholar</span>
                                <button class="install-btn">一键安装</button>
                            </div>
                            <div class="mcp-server">
                                <span class="server-name">🐙 GitHub</span>
                                <button class="install-btn">一键安装</button>
                            </div>
                            <div class="mcp-server">
                                <span class="server-name">📈 Google Analytics</span>
                                <button class="install-btn">一键安装</button>
                            </div>
                        </div>
                    </div>
                    <p class="video-caption">🎬 MCP 服务器一键安装界面演示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>一键安装 MCP 服务器</li>
                        <li>OAuth 身份验证支持</li>
                        <li>官方服务器精选列表</li>
                        <li>开发者友好的集成方式</li>
                    </ul>
                </div>
            </div>

            <!-- Jupyter 支持 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">📊</div>
                    <div>
                        <div class="feature-title">Jupyter Notebooks 智能支持</div>
                        <div class="feature-date">2025年6月 - v1.0</div>
                    </div>
                </div>
                <div class="feature-description">
                    Cursor Agent 现在可以直接在 Jupyter Notebooks 中实现更改！Agent 会创建和编辑多个单元格，显著改善了研究和数据科学任务的体验。目前支持 Sonnet 模型。
                </div>
                <div class="feature-demo">
                    <div class="jupyter-demo">
                        <div class="notebook-interface">
                            <h4>📊 Jupyter Notebook AI 编辑</h4>
                            <div class="notebook-cell">
                                <div class="cell-label">In [1]:</div>
                                <div class="cell-content">
                                    <code>import pandas as pd<br>
                                    df = pd.read_csv('data.csv')<br>
                                    # AI 自动生成数据分析代码</code>
                                </div>
                            </div>
                            <div class="notebook-cell">
                                <div class="cell-label">In [2]:</div>
                                <div class="cell-content">
                                    <code>df.describe()<br>
                                    # AI 添加的统计分析</code>
                                </div>
                            </div>
                            <div class="ai-suggestion">
                                <span class="ai-icon">🤖</span>
                                <span>AI 正在创建新的可视化单元格...</span>
                            </div>
                        </div>
                    </div>
                    <p class="video-caption">🎬 Jupyter Notebooks AI 智能编辑演示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>直接编辑 Jupyter 单元格</li>
                        <li>智能创建新单元格</li>
                        <li>优化数据科学工作流</li>
                        <li>支持复杂的分析任务</li>
                    </ul>
                </div>
            </div>

            <!-- Chat 可视化响应 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">📈</div>
                    <div>
                        <div class="feature-title">丰富的 Chat 响应体验</div>
                        <div class="feature-date">2025年6月 - v1.0</div>
                    </div>
                </div>
                <div class="feature-description">
                    Cursor 现在可以在对话中渲染可视化内容！特别是 Mermaid 图表和 Markdown 表格可以直接在聊天界面中生成和查看，让技术讨论更加直观。
                </div>
                <div class="feature-demo">
                    <div class="chat-demo">
                        <div class="chat-interface">
                            <h4>💬 Chat 可视化响应</h4>
                            <div class="chat-message user">
                                <span class="message-author">用户:</span>
                                <span class="message-content">请创建一个系统架构图</span>
                            </div>
                            <div class="chat-message ai">
                                <span class="message-author">AI:</span>
                                <div class="message-content">
                                    <p>我来为您创建一个系统架构图：</p>
                                    <div class="mermaid-diagram">
                                        <div class="diagram-box">Frontend</div>
                                        <div class="arrow">↓</div>
                                        <div class="diagram-box">API Gateway</div>
                                        <div class="arrow">↓</div>
                                        <div class="diagram-box">Backend Services</div>
                                        <div class="arrow">↓</div>
                                        <div class="diagram-box">Database</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="video-caption">🎬 Chat 界面实时图表渲染演示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>内置 Mermaid 图表渲染</li>
                        <li>Markdown 表格可视化</li>
                        <li>实时图表生成</li>
                        <li>增强技术文档体验</li>
                    </ul>
                </div>
            </div>

            <!-- 新的 Tab 模型 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">⚡</div>
                    <div>
                        <div class="feature-title">全新 Tab 模型 - 多文件智能补全</div>
                        <div class="feature-date">2025年5月 - v0.50</div>
                    </div>
                </div>
                <div class="feature-description">
                    全新训练的 Tab 模型现在可以建议跨多个文件的更改。该模型在重构、编辑链、多文件更改和相关代码跳转方面表现出色，同时在日常使用中感觉更自然、更快速。
                </div>
                <div class="feature-demo">
                    <div class="tab-demo">
                        <div class="code-editor">
                            <h4>⚡ 智能 Tab 补全</h4>
                            <div class="file-tabs">
                                <span class="file-tab active">main.js</span>
                                <span class="file-tab">utils.js</span>
                                <span class="file-tab">api.js</span>
                            </div>
                            <div class="code-content">
                                <div class="code-line">
                                    <span class="line-number">1</span>
                                    <span class="code">import { fetchData } from './api.js';</span>
                                </div>
                                <div class="code-line">
                                    <span class="line-number">2</span>
                                    <span class="code">import { formatDate } from './utils.js';</span>
                                </div>
                                <div class="code-line">
                                    <span class="line-number">3</span>
                                    <span class="code"></span>
                                </div>
                                <div class="code-line">
                                    <span class="line-number">4</span>
                                    <span class="code">const data = await fetch</span>
                                    <span class="tab-suggestion">Data() // Tab 建议</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="video-caption">🎬 跨文件智能 Tab 补全演示</p>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>跨文件智能建议</li>
                        <li>优化重构体验</li>
                        <li>语法高亮补全</li>
                        <li>更快的响应速度</li>
                    </ul>
                </div>
            </div>

            <!-- Max Mode 统一定价 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">💎</div>
                    <div>
                        <div class="feature-title">Max Mode - 统一定价模式</div>
                        <div class="feature-date">2025年5月 - v0.50</div>
                    </div>
                </div>
                <div class="feature-description">
                    Cursor 推出了统一的基于请求的定价模式。Max Mode 现在支持所有顶级模型，采用基于 token 的定价，让用户在面对最困难问题时拥有完全控制权。
                </div>
                <div class="demo-placeholder">
                    <div class="demo-text">🎬 Max Mode 功能展示</div>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>统一的请求定价模式</li>
                        <li>支持所有顶级 AI 模型</li>
                        <li>透明的 token 计费</li>
                        <li>适合复杂编程任务</li>
                    </ul>
                </div>
            </div>

            <!-- Chat Tabs -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">📑</div>
                    <div>
                        <div class="feature-title">Chat Tabs - 并行对话管理</div>
                        <div class="feature-date">2025年3月 - v0.48</div>
                    </div>
                </div>
                <div class="feature-description">
                    Chat Tabs 功能让你可以创建多个聊天标签页，进行并行对话。当某个标签等待你的输入时，会显示橙色提示点，让多任务处理变得更加高效。
                </div>
                <div class="demo-placeholder">
                    <div class="demo-text">🎬 Chat Tabs 多任务处理演示</div>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>多标签并行对话</li>
                        <li>智能状态提示</li>
                        <li>快捷键支持 (⌘T)</li>
                        <li>提升多任务效率</li>
                    </ul>
                </div>
            </div>

            <!-- 自定义模式 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon">🎛️</div>
                    <div>
                        <div class="feature-title">自定义模式 - 个性化工作流</div>
                        <div class="feature-date">2025年3月 - v0.48</div>
                    </div>
                </div>
                <div class="feature-description">
                    自定义模式允许你创建包含特定工具和提示的新模式，完美适配你的工作流程。每个自定义模式都可以设置专属快捷键，让 AI 助手真正为你量身定制。
                </div>
                <div class="demo-placeholder">
                    <div class="demo-text">🎬 自定义模式配置演示</div>
                </div>
                <div class="feature-highlights">
                    <h4>核心亮点：</h4>
                    <ul>
                        <li>完全自定义的 AI 模式</li>
                        <li>个性化工具组合</li>
                        <li>自定义快捷键绑定</li>
                        <li>适配特定工作流程</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🌟 Cursor IDE - 重新定义 AI 辅助编程体验</p>
            <p>数据来源：Cursor 官方更新日志 | 更新时间：2025年6月</p>
        </div>
    </div>

    <script>
        // 添加滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有特性卡片
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // 添加点击效果
        document.querySelectorAll('.demo-placeholder').forEach(placeholder => {
            placeholder.addEventListener('click', function() {
                this.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                this.querySelector('.demo-text').style.color = 'white';
                this.querySelector('.demo-text').textContent = '🎬 点击查看完整演示 (演示功能开发中...)';

                setTimeout(() => {
                    this.style.background = 'linear-gradient(45deg, #f0f0f0, #e0e0e0)';
                    this.querySelector('.demo-text').style.color = '#666';
                    this.querySelector('.demo-text').textContent = '🎬 演示动画占位符';
                }, 2000);
            });
        });
    </script>
</body>
</html>
